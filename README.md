# PyQt6自动分类程序

这是一个基于PyQt6开发的自动分类程序，用于根据Excel表格数据自动分类文件夹。

## 功能特点

- 读取Excel文件中的"任务支出"工作表
- **智能处理Excel格式问题**（科学计数法、大数字、空格等）
- **日期筛选功能**，支持按日期范围筛选记录
- **支持多种文件夹格式**（纯数字、日期-数字格式如250731-032906975022674）
- 根据原始单号匹配文件夹名称
- 按店铺名称自动创建分类目录
- 将匹配的文件夹移动到对应的店铺目录中
- **店铺汇总统计**，详细显示每个店铺的处理情况
- 提供图形化界面，操作简单直观
- **详细的操作日志**，清楚显示匹配和错误信息
- 实时显示操作进度和统计数据

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行程序：
   ```bash
   python main.py
   ```

2. 在界面中选择：
   - Excel文件：包含"任务支出"工作表的Excel文件
   - 文件夹路径：包含数字文件夹的目录

3. 点击"开始分类"按钮

## Excel表格要求

程序要求Excel文件包含名为"任务支出"的工作表，并且包含以下列：

- 姓名
- 店铺
- 日期
- 原始单号
- 客户网名
- 金额
- 礼品
- 返款码
- 支出渠道
- 备注
- 操作手机
- 手机号码
- 产品名称
- 关键词

## 文件夹要求

- 文件夹名称支持以下格式（对应Excel中的原始单号）：
  - **纯数字格式**：`123456789`
  - **日期-数字格式**：`250731-032906975022674`（YYMMDD-数字）
- 程序会根据原始单号匹配文件夹
- 匹配成功的文件夹会被移动到以店铺名称命名的子目录中

## 分类逻辑

1. 扫描指定目录下的所有数字文件夹
2. 读取Excel表格中的原始单号和店铺信息
3. 将文件夹名称与原始单号进行匹配
4. 根据匹配结果获取对应的店铺名称
5. 创建以店铺名称命名的目录（如果不存在）
6. 将文件夹移动到对应的店铺目录中

## 日期筛选功能

程序新增了日期筛选功能，可以根据指定日期筛选Excel记录：

### 使用方法
1. **启用日期筛选**：勾选"启用日期筛选"复选框
2. **选择筛选日期**：使用日期选择器设置筛选的起始日期
3. **筛选逻辑**：程序将只处理选定日期及之后的记录

### 支持的日期格式
- ✅ `2025/7/28 10:32:55` (标准格式)
- ✅ `2025-07-28 10:32:55`
- ✅ `2025/7/28`
- ✅ `2025-07-28`
- ✅ 其他常见日期格式

### 筛选说明
- 📅 **单日期筛选**：只需设置一个日期，筛选该日期及之后的所有记录
- 🔍 **智能解析**：自动识别Excel中的各种日期格式
- 📊 **详细统计**：显示筛选前后的记录数量变化
- ⚠️ **数据过滤**：日期解析失败的记录会被自动过滤掉

## Excel格式处理

程序已优化处理各种Excel格式问题：

- ✅ **科学计数法格式** - 自动转换 `2.845441e+17` 为正确的数字字符串
- ✅ **大整数显示问题** - 正确处理超长数字
- ✅ **空格和换行符** - 自动清理字符串前后的空白字符
- ✅ **空值处理** - 过滤掉空值、NaN等无效数据
- ✅ **数据类型混合** - 统一转换为字符串格式进行匹配

## 详细日志功能

程序提供详细的操作日志，帮助用户了解处理过程：

- 📋 显示Excel中所有原始单号
- 📂 列出扫描到的所有数字文件夹
- ✅ 明确指出哪些文件夹可以匹配
- ❌ **清楚显示找不到匹配的位置**：
  - 文件夹在Excel表格中找不到对应原始单号
  - 原始单号在文件夹目录中找不到对应文件夹
- 🏪 按店铺分组显示分类预览
- 📊 提供详细的匹配统计信息

## 注意事项

- 请确保Excel文件格式正确，包含所需的工作表和列
- 建议在操作前备份重要文件
- 如果目标位置已存在同名文件夹，程序会跳过移动操作
- 程序会显示详细的操作日志，便于跟踪处理过程
- **原始单号支持各种格式**，程序会自动处理格式转换

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在检查
- Excel格式验证
- 文件移动异常处理
- 用户操作中断处理
