#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt6自动分类程序
根据Excel表格数据自动分类文件夹
"""

import sys
import os
import shutil
import pandas as pd
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QLineEdit, QFileDialog,
                             QTextEdit, QProgressBar, QMessageBox, QGroupBox,
                             QDateEdit, QCheckBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QDate
from datetime import datetime, timedelta


def clean_order_id_data(df):
    """
    清理Excel中的原始单号数据，解决科学计数法等格式问题
    """
    # 确保原始单号列存在
    if '原始单号' not in df.columns:
        raise ValueError("Excel文件中未找到'原始单号'列")

    # 转换为字符串并清理
    df['原始单号'] = df['原始单号'].astype(str).str.strip()

    # 处理科学计数法格式的数字
    def fix_scientific_notation(value):
        try:
            # 如果是科学计数法格式，转换为整数字符串
            if 'e+' in value.lower() or 'e-' in value.lower():
                # 尝试转换为浮点数再转为整数
                float_val = float(value)
                if float_val.is_integer():
                    return str(int(float_val))
                else:
                    return str(int(round(float_val)))
            return value
        except (ValueError, TypeError):
            return value

    df['原始单号'] = df['原始单号'].apply(fix_scientific_notation)

    # 过滤掉空值和无效值
    original_count = len(df)
    df = df[df['原始单号'].notna() & (df['原始单号'] != '') & (df['原始单号'] != 'nan')]
    filtered_count = original_count - len(df)

    return df, filtered_count


def parse_date_column(df):
    """
    解析Excel中的日期列，支持多种日期格式
    """
    if '日期' not in df.columns:
        raise ValueError("Excel文件中未找到'日期'列")

    # 尝试解析日期
    def parse_date(date_value):
        if pd.isna(date_value):
            return None

        # 如果已经是datetime类型，直接返回
        if isinstance(date_value, datetime):
            return date_value

        # 转换为字符串
        date_str = str(date_value).strip()

        # 支持的日期格式
        date_formats = [
            '%Y/%m/%d %H:%M:%S',    # 2025/7/28 10:32:55
            '%Y-%m-%d %H:%M:%S',    # 2025-07-28 10:32:55
            '%Y/%m/%d',             # 2025/7/28
            '%Y-%m-%d',             # 2025-07-28
            '%m/%d/%Y',             # 7/28/2025
            '%d/%m/%Y',             # 28/7/2025
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        # 如果都失败了，尝试pandas的自动解析
        try:
            return pd.to_datetime(date_str)
        except:
            return None

    # 解析日期列
    df['日期_parsed'] = df['日期'].apply(parse_date)

    # 统计解析结果
    valid_dates = df['日期_parsed'].notna().sum()
    total_dates = len(df)

    return df, valid_dates, total_dates


def filter_by_date_range(df, start_date=None, end_date=None):
    """
    根据日期范围筛选数据
    """
    if '日期_parsed' not in df.columns:
        df, _, _ = parse_date_column(df)

    original_count = len(df)

    # 过滤掉日期解析失败的记录
    df_filtered = df[df['日期_parsed'].notna()].copy()

    if start_date:
        # 将Python date转换为datetime进行比较
        if hasattr(start_date, 'date'):
            # 如果是datetime对象，取日期部分
            start_datetime = start_date
        else:
            # 如果是date对象，转换为datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())

        df_filtered = df_filtered[df_filtered['日期_parsed'] >= start_datetime]

    if end_date:
        # 将Python date转换为datetime进行比较
        if hasattr(end_date, 'date'):
            # 如果是datetime对象，取日期部分
            end_datetime = end_date + timedelta(days=1) - timedelta(seconds=1)
        else:
            # 如果是date对象，转换为datetime，包含当天的23:59:59
            end_datetime = datetime.combine(end_date, datetime.max.time())

        df_filtered = df_filtered[df_filtered['日期_parsed'] <= end_datetime]

    filtered_count = original_count - len(df_filtered)

    return df_filtered, filtered_count


class ClassificationWorker(QThread):
    """分类工作线程"""
    progress_updated = pyqtSignal(int)
    log_updated = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, excel_path, folder_path, enable_date_filter=False, filter_date=None):
        super().__init__()
        self.excel_path = excel_path
        self.folder_path = folder_path
        self.enable_date_filter = enable_date_filter
        self.filter_date = filter_date

    def is_valid_folder_name(self, folder_name):
        """
        检查文件夹名称是否为有效的订单号格式
        支持以下格式:
        - 纯数字: 123456789
        - 日期-数字格式: 250731-032906975022674
        """
        import re

        # 纯数字格式
        if folder_name.isdigit():
            return True

        # 日期-数字格式 (YYMMDD-数字)
        pattern = r'^\d{6}-\d+$'
        if re.match(pattern, folder_name):
            return True

        return False
        
    def run(self):
        try:
            self.log_updated.emit("=" * 50)
            self.log_updated.emit("开始自动分类任务")
            self.log_updated.emit("=" * 50)

            # 读取Excel文件
            self.log_updated.emit("📖 正在读取Excel文件...")
            # 指定原始单号列为文本格式，避免科学计数法
            df = pd.read_excel(self.excel_path, sheet_name='任务支出', dtype={'原始单号': str})
            original_count = len(df)
            self.log_updated.emit(f"✅ 成功读取Excel文件，共{original_count}条记录")

            # 处理日期筛选
            if self.enable_date_filter:
                self.log_updated.emit("📅 正在解析日期数据...")
                df, valid_dates, total_dates = parse_date_column(df)
                self.log_updated.emit(f"✅ 日期解析完成: {valid_dates}/{total_dates} 条记录有有效日期")

                if valid_dates == 0:
                    self.finished.emit(False, "❌ 没有找到有效的日期数据，无法进行日期筛选")
                    return

                self.log_updated.emit("🔍 正在应用日期筛选...")
                filter_str = self.filter_date.strftime('%Y-%m-%d') if self.filter_date else "无限制"
                self.log_updated.emit(f"📅 筛选条件: {filter_str} 及之后的记录")

                df, date_filtered_count = filter_by_date_range(df, self.filter_date, None)
                if date_filtered_count > 0:
                    self.log_updated.emit(f"⚠️  日期筛选过滤掉{date_filtered_count}条记录，剩余{len(df)}条记录")

                if len(df) == 0:
                    self.finished.emit(False, "❌ 日期筛选后没有剩余记录")
                    return

            # 清理原始单号数据
            self.log_updated.emit("🔧 正在清理原始单号数据格式...")
            df, filtered_count = clean_order_id_data(df)
            if filtered_count > 0:
                self.log_updated.emit(f"⚠️  过滤掉{filtered_count}条无效记录，剩余{len(df)}条有效记录")

            # 显示Excel中的原始单号列表
            excel_order_ids = df['原始单号'].tolist()
            self.log_updated.emit(f"📋 Excel中的原始单号: {', '.join(excel_order_ids[:10])}{'...' if len(excel_order_ids) > 10 else ''}")

            # 获取所有文件夹
            self.log_updated.emit("📁 正在扫描文件夹...")
            folder_path = Path(self.folder_path)
            all_folders = [f for f in folder_path.iterdir() if f.is_dir() and self.is_valid_folder_name(f.name)]
            folder_names = [f.name for f in all_folders]
            self.log_updated.emit(f"✅ 找到{len(all_folders)}个有效文件夹")
            self.log_updated.emit(f"📂 文件夹列表: {', '.join(folder_names[:10])}{'...' if len(folder_names) > 10 else ''}")

            if not all_folders:
                self.finished.emit(False, "❌ 未找到任何有效的订单号文件夹（支持纯数字或YYMMDD-数字格式）")
                return

            # 分析匹配情况
            self.log_updated.emit("\n🔍 开始匹配分析...")
            matched_folders = []
            unmatched_folders = []

            for folder in all_folders:
                folder_name = folder.name
                if folder_name in excel_order_ids:
                    matched_folders.append(folder_name)
                else:
                    unmatched_folders.append(folder_name)

            self.log_updated.emit(f"✅ 可匹配的文件夹: {len(matched_folders)}个")
            self.log_updated.emit(f"❌ 无法匹配的文件夹: {len(unmatched_folders)}个")

            if unmatched_folders:
                self.log_updated.emit(f"⚠️  以下文件夹在Excel表格中找不到对应的原始单号:")
                for folder_name in unmatched_folders:
                    self.log_updated.emit(f"   - 文件夹 '{folder_name}' → Excel表格中无此原始单号")

            # 检查Excel中有但文件夹中没有的单号
            missing_folders = [order_id for order_id in excel_order_ids if order_id not in folder_names]
            if missing_folders:
                self.log_updated.emit(f"⚠️  以下原始单号在文件夹中找不到对应的文件夹:")
                for order_id in missing_folders:
                    self.log_updated.emit(f"   - 原始单号 '{order_id}' → 文件夹目录中无此文件夹")

            # 显示店铺分类预览
            self.log_updated.emit("\n🏪 店铺分类预览:")
            shop_preview = {}
            for folder_name in matched_folders:
                matching_rows = df[df['原始单号'] == folder_name]
                if not matching_rows.empty:
                    shop_name = matching_rows.iloc[0]['店铺']
                    if shop_name not in shop_preview:
                        shop_preview[shop_name] = []
                    shop_preview[shop_name].append(folder_name)

            if shop_preview:
                for shop_name, folders in sorted(shop_preview.items()):
                    folder_count = len(folders)
                    if folder_count <= 5:
                        folder_display = ', '.join(sorted(folders))
                    else:
                        folder_display = f"{', '.join(sorted(folders[:5]))}... (共{folder_count}个)"
                    self.log_updated.emit(f"   📍 {shop_name}: {folder_display}")
            else:
                self.log_updated.emit("   ❌ 没有可匹配的店铺分类")

            self.log_updated.emit("\n🚀 开始移动文件夹...")
            processed_count = 0
            moved_count = 0
            skipped_count = 0

            # 店铺汇总统计
            shop_summary = {}

            for folder in all_folders:
                folder_name = folder.name

                # 在Excel中查找匹配的原始单号
                matching_rows = df[df['原始单号'] == folder_name]

                if not matching_rows.empty:
                    # 获取店铺名称
                    shop_name = matching_rows.iloc[0]['店铺']

                    # 初始化店铺统计信息
                    if shop_name not in shop_summary:
                        shop_summary[shop_name] = {
                            'total_folders': 0,
                            'moved_folders': 0,
                            'skipped_folders': 0,
                            'folder_list': []
                        }

                    shop_summary[shop_name]['total_folders'] += 1
                    shop_summary[shop_name]['folder_list'].append(folder_name)

                    # 创建店铺目录
                    shop_dir = folder_path / shop_name
                    shop_dir.mkdir(exist_ok=True)

                    # 移动文件夹
                    target_path = shop_dir / folder_name
                    if not target_path.exists():
                        shutil.move(str(folder), str(target_path))
                        moved_count += 1
                        shop_summary[shop_name]['moved_folders'] += 1
                        self.log_updated.emit(f"✅ 成功移动: 文件夹 '{folder_name}' → '{shop_name}' 目录")
                    else:
                        skipped_count += 1
                        shop_summary[shop_name]['skipped_folders'] += 1
                        self.log_updated.emit(f"⚠️  跳过移动: 文件夹 '{folder_name}' → 目标路径 '{target_path}' 已存在")
                else:
                    skipped_count += 1
                    self.log_updated.emit(f"❌ 跳过移动: 文件夹 '{folder_name}' → Excel表格中找不到此原始单号")

                processed_count += 1
                progress = int((processed_count / len(all_folders)) * 100)
                self.progress_updated.emit(progress)

            # 输出店铺汇总统计
            if shop_summary:
                self.log_updated.emit("\n" + "=" * 50)
                self.log_updated.emit("🏪 店铺汇总统计:")
                self.log_updated.emit("=" * 50)

                # 按店铺名称排序
                sorted_shops = sorted(shop_summary.items(), key=lambda x: x[1]['total_folders'], reverse=True)

                for shop_name, stats in sorted_shops:
                    self.log_updated.emit(f"\n📍 {shop_name}:")
                    self.log_updated.emit(f"   总文件夹数: {stats['total_folders']}个")
                    self.log_updated.emit(f"   成功移动: {stats['moved_folders']}个")
                    if stats['skipped_folders'] > 0:
                        self.log_updated.emit(f"   跳过处理: {stats['skipped_folders']}个")

                    # 显示文件夹列表（限制显示数量）
                    folder_list = stats['folder_list']
                    if len(folder_list) <= 10:
                        self.log_updated.emit(f"   文件夹: {', '.join(sorted(folder_list))}")
                    else:
                        self.log_updated.emit(f"   文件夹: {', '.join(sorted(folder_list[:10]))}... (共{len(folder_list)}个)")

            # 输出最终统计
            self.log_updated.emit("\n" + "=" * 50)
            self.log_updated.emit("📊 总体统计:")
            self.log_updated.emit(f"   总处理文件夹: {processed_count}个")
            self.log_updated.emit(f"   成功移动: {moved_count}个")
            self.log_updated.emit(f"   跳过处理: {skipped_count}个")
            self.log_updated.emit(f"   匹配率: {len(matched_folders)}/{len(all_folders)} ({len(matched_folders)/len(all_folders)*100:.1f}%)")
            if shop_summary:
                self.log_updated.emit(f"   涉及店铺: {len(shop_summary)}个")
            self.log_updated.emit("=" * 50)

            self.finished.emit(True, f"分类完成！成功移动{moved_count}个文件夹，跳过{skipped_count}个文件夹")

        except Exception as e:
            self.log_updated.emit(f"❌ 分类过程中出现错误: {str(e)}")
            self.finished.emit(False, f"分类过程中出现错误: {str(e)}")


class AutoClassifierApp(QMainWindow):
    """主应用程序窗口"""
    
    def __init__(self):
        super().__init__()
        self.excel_path = ""
        self.folder_path = ""
        self.worker = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Excel文件夹自动分类工具")
        self.setGeometry(100, 100, 900, 850)
        self.setMinimumSize(900, 850)

        # 设置现代化样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                margin-top: 1ex;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);

            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #495057;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4dabf7, stop:1 #339af0);
                border: none;
                color: white;
                padding: 12px 24px;
                text-align: center;
                font-size: 14px;
                border-radius: 8px;
                font-weight: bold;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #339af0, stop:1 #228be6);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #228be6, stop:1 #1c7ed6);
            }
            QPushButton:disabled {
                background: #adb5bd;
                color: #6c757d;
            }
            QLineEdit {
                border: 2px solid #ced4da;
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #4dabf7;
            }
            QLineEdit:focus {
                border-color: #4dabf7;
            }
            QTextEdit {
                border: 2px solid #ced4da;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 12px;
                background-color: #212529;
                color: #f8f9fa;
                selection-background-color: #495057;
            }
            QProgressBar {
                border: 2px solid #ced4da;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #f8f9fa;
                color: #495057;
                min-height: 20px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #51cf66, stop:1 #40c057);
                border-radius: 6px;
                margin: 1px;
            }
            QCheckBox {
                font-size: 13px;
                color: #495057;
                spacing: 10px;
                font-weight: 500;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 4px;
                border: 2px solid #ced4da;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4dabf7;
                border-color: #4dabf7;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:hover {
                border-color: #4dabf7;
            }
            QDateEdit {
                border: 2px solid #ced4da;
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 13px;
                background-color: white;
                min-width: 140px;
            }
            QDateEdit:focus {
                border-color: #4dabf7;
            }
            QDateEdit::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #ced4da;
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
                background-color: #f8f9fa;
            }
            QDateEdit::drop-down:hover {
                background-color: #e9ecef;
            }
            QDateEdit::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzQ5NTA1NyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
            QLabel {
                color: #495057;
                font-size: 13px;
            }
        """)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # 创建美化的标题
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("🗂️ Excel文件夹自动分类工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 24px;
                font-weight: bold;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(77, 171, 247, 0.1),
                    stop:0.5 rgba(77, 171, 247, 0.05),
                    stop:1 rgba(77, 171, 247, 0.1));
                border-radius: 12px;
                border: 1px solid rgba(77, 171, 247, 0.2);
            }
        """)

        subtitle_label = QLabel("智能处理Excel数据，按店铺自动分类文件夹")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                font-style: italic;
                margin-top: 5px;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        main_layout.addWidget(title_widget)
        
        # 文件选择组
        file_group = QGroupBox("📁 文件和路径选择")
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(15)

        # Excel文件选择
        excel_layout = QHBoxLayout()
        excel_label = QLabel("📊 Excel文件:")
        excel_label.setMinimumWidth(100)
        excel_label.setStyleSheet("font-weight: bold; color: #495057;")
        excel_layout.addWidget(excel_label)

        self.excel_path_edit = QLineEdit()
        self.excel_path_edit.setPlaceholderText("请选择包含'任务支出'工作表的Excel文件...")
        excel_layout.addWidget(self.excel_path_edit)

        self.excel_browse_btn = QPushButton("浏览")
        self.excel_browse_btn.setFixedSize(80, 32)
        self.excel_browse_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                padding: 6px 12px;
                font-weight: bold;
            }
        """)
        self.excel_browse_btn.clicked.connect(self.browse_excel_file)
        excel_layout.addWidget(self.excel_browse_btn)
        file_layout.addLayout(excel_layout)

        # 文件夹路径选择
        folder_layout = QHBoxLayout()
        folder_label = QLabel("📂 文件夹路径:")
        folder_label.setMinimumWidth(100)
        folder_label.setStyleSheet("font-weight: bold; color: #495057;")
        folder_layout.addWidget(folder_label)

        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setPlaceholderText("请选择包含订单号文件夹的目录（支持纯数字或YYMMDD-数字格式）...")
        folder_layout.addWidget(self.folder_path_edit)

        self.folder_browse_btn = QPushButton("浏览")
        self.folder_browse_btn.setFixedSize(80, 32)
        self.folder_browse_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                padding: 6px 12px;
                font-weight: bold;
            }
        """)
        self.folder_browse_btn.clicked.connect(self.browse_folder)
        folder_layout.addWidget(self.folder_browse_btn)
        file_layout.addLayout(folder_layout)

        # 文件夹格式说明
        folder_info_widget = QWidget()
        folder_info_widget.setStyleSheet("""
            background-color: rgba(77, 171, 247, 0.1);
            border-radius: 8px;
            padding: 10px;
            border: 1px solid rgba(77, 171, 247, 0.2);
        """)
        folder_info_layout = QHBoxLayout(folder_info_widget)
        folder_info_layout.setContentsMargins(10, 5, 10, 5)

        folder_info_label = QLabel("💡 支持的文件夹格式: 纯数字(123456789) 或 日期-数字(250731-032906975022674)")
        folder_info_label.setStyleSheet("""
            color: #495057;
            font-size: 12px;
            font-weight: 500;
        """)
        folder_info_layout.addWidget(folder_info_label)
        file_layout.addWidget(folder_info_widget)

        main_layout.addWidget(file_group)

        # 日期筛选组
        date_group = QGroupBox("📅 日期筛选（可选）")
        date_layout = QVBoxLayout(date_group)
        date_layout.setSpacing(15)

        # 启用日期筛选复选框
        self.enable_date_filter = QCheckBox("✅ 启用日期筛选")
        self.enable_date_filter.setStyleSheet("font-weight: bold; color: #495057;")
        self.enable_date_filter.stateChanged.connect(self.toggle_date_filter)
        date_layout.addWidget(self.enable_date_filter)

        # 日期选择容器
        date_container = QWidget()
        date_range_layout = QHBoxLayout(date_container)
        date_range_layout.setContentsMargins(20, 0, 0, 0)

        # 筛选日期
        date_label = QLabel("📆 筛选日期:")
        date_label.setMinimumWidth(100)
        date_label.setStyleSheet("font-weight: bold; color: #495057;")
        date_range_layout.addWidget(date_label)

        self.filter_date_edit = QDateEdit()
        self.filter_date_edit.setDate(QDate.currentDate().addDays(-7))  # 默认7天前
        self.filter_date_edit.setCalendarPopup(True)
        self.filter_date_edit.setEnabled(False)
        self.filter_date_edit.setDisplayFormat("yyyy-MM-dd")
        date_range_layout.addWidget(self.filter_date_edit)

        # 说明标签
        filter_info_label = QLabel("（筛选此日期及之后的记录）")
        filter_info_label.setStyleSheet("""
            color: #6c757d;
            font-size: 12px;
            font-style: italic;
            margin-left: 10px;
        """)
        date_range_layout.addWidget(filter_info_label)
        date_range_layout.addStretch()

        date_layout.addWidget(date_container)

        # 日期格式说明
        date_info_widget = QWidget()
        date_info_widget.setStyleSheet("""
            background-color: rgba(77, 171, 247, 0.1);
            border-radius: 8px;
            padding: 10px;
            border: 1px solid rgba(77, 171, 247, 0.2);
        """)
        date_info_layout = QHBoxLayout(date_info_widget)
        date_info_layout.setContentsMargins(10, 5, 10, 5)

        date_info_label = QLabel("💡 支持的Excel日期格式: 2025/7/28 10:32:55, 2025-07-28 等")
        date_info_label.setStyleSheet("""
            color: #495057;
            font-size: 12px;
            font-weight: 500;
        """)
        date_info_layout.addWidget(date_info_label)
        date_layout.addWidget(date_info_widget)

        main_layout.addWidget(date_group)

        # 操作按钮区域
        button_container = QWidget()
        button_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-radius: 12px;
                border: 1px solid #dee2e6;
                padding: 15px;
            }
        """)
        button_layout = QHBoxLayout(button_container)
        button_layout.setSpacing(10)
        button_layout.addStretch()  # 左侧弹性空间

        # 开始按钮
        self.start_btn = QPushButton("🚀 开始分类")
        self.start_btn.clicked.connect(self.start_classification)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #51cf66, stop:1 #40c057);
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 6px;
                min-height: 15px;
                max-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40c057, stop:1 #37b24d);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #37b24d, stop:1 #2f9e44);
            }
        """)
        button_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.clicked.connect(self.stop_classification)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff6b6b, stop:1 #fa5252);
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 6px;
                min-height: 15px;
                max-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fa5252, stop:1 #f03e3e);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f03e3e, stop:1 #e03131);
            }
            QPushButton:disabled {
                background: #adb5bd;
                color: #6c757d;
            }
        """)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()  # 右侧弹性空间

        main_layout.addWidget(button_container)

        # 进度显示区域
        progress_container = QWidget()
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(0, 0, 0, 0)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                text-align: center;
                font-weight: bold;
                background-color: #f8f9fa;
                color: #495057;
                min-height: 25px;
                font-size: 14px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #51cf66, stop:1 #40c057);
                border-radius: 8px;
                margin: 1px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        main_layout.addWidget(progress_container)

        # 日志显示
        log_group = QGroupBox("📋 操作日志")
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(15, 20, 15, 15)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(200)
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 13px;
                background-color: #212529;
                color: #f8f9fa;
                selection-background-color: #495057;
                line-height: 1.4;
            }
        """)
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group)
        
    def browse_excel_file(self):
        """浏览Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel文件 (*.xlsx *.xls)"
        )
        if file_path:
            self.excel_path = file_path
            self.excel_path_edit.setText(file_path)
            
    def browse_folder(self):
        """浏览文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder_path:
            self.folder_path = folder_path
            self.folder_path_edit.setText(folder_path)

    def toggle_date_filter(self, state):
        """切换日期筛选功能"""
        enabled = state == Qt.CheckState.Checked.value
        self.filter_date_edit.setEnabled(enabled)
            
    def start_classification(self):
        """开始分类"""
        if not self.excel_path or not self.folder_path:
            QMessageBox.warning(self, "警告", "请先选择Excel文件和文件夹路径！")
            return
            
        if not os.path.exists(self.excel_path):
            QMessageBox.warning(self, "警告", "Excel文件不存在！")
            return
            
        if not os.path.exists(self.folder_path):
            QMessageBox.warning(self, "警告", "文件夹路径不存在！")
            return
            
        # 获取日期筛选参数
        enable_date_filter = self.enable_date_filter.isChecked()
        filter_date = None

        if enable_date_filter:
            filter_date = self.filter_date_edit.date().toPyDate()

        # 启动工作线程
        self.worker = ClassificationWorker(
            self.excel_path,
            self.folder_path,
            enable_date_filter,
            filter_date
        )
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.log_updated.connect(self.update_log)
        self.worker.finished.connect(self.classification_finished)
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        self.worker.start()
        
    def stop_classification(self):
        """停止分类"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
            self.update_log("用户停止了分类操作")
            self.classification_finished(False, "操作已停止")
            
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
        
    def update_log(self, message):
        """更新日志"""
        self.log_text.append(message)
        
    def classification_finished(self, success, message):
        """分类完成"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.warning(self, "错误", message)
            
        self.update_log(f"操作完成: {message}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用现代风格
    
    window = AutoClassifierApp()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
