#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示改进后的日志输出功能
"""

import pandas as pd
from pathlib import Path


def demo_log_analysis():
    """演示日志分析功能"""
    print("=" * 60)
    print("🎯 PyQt6自动分类程序 - 改进后的日志功能演示")
    print("=" * 60)
    
    # 读取测试数据
    excel_path = "测试数据.xlsx"
    folder_path = Path("测试文件夹")
    
    if not Path(excel_path).exists():
        print("❌ 请先运行 'python create_test_data.py' 创建测试数据")
        return
    
    print("📖 正在读取Excel文件...")
    df = pd.read_excel(excel_path, sheet_name='任务支出', dtype={'原始单号': str})
    print(f"✅ 成功读取Excel文件，共{len(df)}条记录")

    # 清理原始单号数据
    df['原始单号'] = df['原始单号'].astype(str).str.strip()
    df = df[df['原始单号'].notna() & (df['原始单号'] != '') & (df['原始单号'] != 'nan')]

    # 显示Excel中的原始单号列表
    excel_order_ids = df['原始单号'].tolist()
    print(f"📋 Excel中的原始单号: {', '.join(excel_order_ids)}")
    
    # 获取所有文件夹
    print("📁 正在扫描文件夹...")
    all_folders = [f for f in folder_path.iterdir() if f.is_dir() and f.name.isdigit()]
    folder_names = [f.name for f in all_folders]
    print(f"✅ 找到{len(all_folders)}个数字文件夹")
    print(f"📂 文件夹列表: {', '.join(sorted(folder_names))}")
    
    # 分析匹配情况
    print("\n🔍 开始匹配分析...")
    matched_folders = []
    unmatched_folders = []
    
    for folder in all_folders:
        folder_name = folder.name
        if folder_name in excel_order_ids:
            matched_folders.append(folder_name)
        else:
            unmatched_folders.append(folder_name)
    
    print(f"✅ 可匹配的文件夹: {len(matched_folders)}个")
    print(f"❌ 无法匹配的文件夹: {len(unmatched_folders)}个")
    
    if unmatched_folders:
        print(f"\n⚠️  以下文件夹在Excel表格中找不到对应的原始单号:")
        for folder_name in sorted(unmatched_folders):
            print(f"   - 文件夹 '{folder_name}' → Excel表格中无此原始单号")
    
    # 检查Excel中有但文件夹中没有的单号
    missing_folders = [order_id for order_id in excel_order_ids if order_id not in folder_names]
    if missing_folders:
        print(f"\n⚠️  以下原始单号在文件夹中找不到对应的文件夹:")
        for order_id in sorted(missing_folders):
            print(f"   - 原始单号 '{order_id}' → 文件夹目录中无此文件夹")
    
    # 显示分类预览
    print(f"\n🏪 店铺分类预览:")
    shop_groups = df.groupby('店铺')['原始单号'].apply(lambda x: x.tolist()).to_dict()
    
    for shop_name, order_ids in shop_groups.items():
        available_folders = [oid for oid in order_ids if oid in folder_names]
        missing_folders_for_shop = [oid for oid in order_ids if oid not in folder_names]
        
        print(f"\n   📍 {shop_name}:")
        if available_folders:
            print(f"      ✅ 可移动文件夹: {', '.join(sorted(available_folders))}")
        if missing_folders_for_shop:
            print(f"      ❌ 缺失文件夹: {', '.join(sorted(missing_folders_for_shop))}")
    
    # 输出统计信息
    print(f"\n📊 匹配统计:")
    print(f"   总数字文件夹: {len(all_folders)}个")
    print(f"   可匹配文件夹: {len(matched_folders)}个")
    print(f"   无法匹配文件夹: {len(unmatched_folders)}个")
    print(f"   匹配率: {len(matched_folders)}/{len(all_folders)} ({len(matched_folders)/len(all_folders)*100:.1f}%)")
    
    print(f"\n   Excel记录总数: {len(df)}条")
    print(f"   有对应文件夹: {len([oid for oid in excel_order_ids if oid in folder_names])}条")
    print(f"   缺失文件夹: {len(missing_folders)}条")
    
    print("\n" + "=" * 60)
    print("💡 这就是改进后的日志功能！")
    print("   - 清楚显示哪些文件夹在Excel中找不到")
    print("   - 明确指出哪些原始单号在文件夹中缺失")
    print("   - 提供详细的匹配统计信息")
    print("   - 按店铺分组显示分类预览")
    print("=" * 60)


if __name__ == "__main__":
    demo_log_analysis()
